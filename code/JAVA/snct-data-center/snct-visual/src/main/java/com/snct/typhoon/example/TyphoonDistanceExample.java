package com.snct.typhoon.example;

import com.snct.typhoon.domain.vo.ActiveTyphoonSummary;
import com.snct.typhoon.service.impl.TyphoonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: TyphoonDistanceExample
 * @Description: 台风距离计算和预警示例
 * @author: wzewei
 * @date: 2025-08-20 16:35:00
 */
@Component
public class TyphoonDistanceExample {

    @Autowired
    private TyphoonService typhoonService;

    /**
     * 计算船只与活跃台风的距离并进行预警判断
     *
     * @param shipLat 船只纬度
     * @param shipLng 船只经度
     * @param warningDistance 预警距离（公里）
     */
    public void checkTyphoonWarning(double shipLat, double shipLng, double warningDistance) {
        try {
            // 获取活跃台风摘要信息
            Map<String, Object> activeTyphoonData = typhoonService.getActiveTyphoonSummary();
            
            if (activeTyphoonData.isEmpty()) {
                System.out.println("当前无活跃台风");
                return;
            }
            
            Integer count = (Integer) activeTyphoonData.get("count");
            String updateTime = (String) activeTyphoonData.get("updateTime");
            List<Map<String, Object>> typhoons = (List<Map<String, Object>>) activeTyphoonData.get("typhoons");
            
            System.out.println("=== 活跃台风预警检查 ===");
            System.out.println("数据更新时间: " + updateTime);
            System.out.println("活跃台风数量: " + count);
            System.out.println("船只位置: " + shipLat + ", " + shipLng);
            System.out.println("预警距离: " + warningDistance + " 公里");
            System.out.println();
            
            boolean hasWarning = false;
            
            for (Map<String, Object> typhoon : typhoons) {
                String tfid = (String) typhoon.get("tfid");
                String name = (String) typhoon.get("name");
                String centerLat = (String) typhoon.get("centerLat");
                String centerLng = (String) typhoon.get("centerLng");
                String strong = (String) typhoon.get("strong");
                String power = (String) typhoon.get("power");
                String typhoonUpdateTime = (String) typhoon.get("updateTime");
                
                // 计算距离
                double distance = calculateDistance(shipLat, shipLng, 
                    Double.parseDouble(centerLat), Double.parseDouble(centerLng));
                
                System.out.println("台风信息:");
                System.out.println("  编号: " + tfid);
                System.out.println("  名称: " + name);
                System.out.println("  位置: " + centerLat + ", " + centerLng);
                System.out.println("  强度: " + strong + " (风力" + power + "级)");
                System.out.println("  更新时间: " + typhoonUpdateTime);
                System.out.println("  距离: " + String.format("%.2f", distance) + " 公里");
                
                if (distance < warningDistance) {
                    System.out.println("  ⚠️ 预警: 船只距离台风过近，请注意安全！");
                    hasWarning = true;
                } else {
                    System.out.println("  ✅ 安全: 距离在安全范围内");
                }
                System.out.println();
            }
            
            if (!hasWarning) {
                System.out.println("✅ 所有台风距离均在安全范围内");
            }
            
        } catch (Exception e) {
            System.err.println("台风预警检查失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 计算两点间距离（使用Haversine公式）
     *
     * @param lat1 点1纬度
     * @param lng1 点1经度
     * @param lat2 点2纬度
     * @param lng2 点2经度
     * @return 距离（公里）
     */
    private double calculateDistance(double lat1, double lng1, double lat2, double lng2) {
        final double R = 6371; // 地球半径（公里）
        
        double latDistance = Math.toRadians(lat2 - lat1);
        double lngDistance = Math.toRadians(lng2 - lng1);
        
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lngDistance / 2) * Math.sin(lngDistance / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return R * c;
    }

    /**
     * 获取指定台风的位置信息
     *
     * @param tfid 台风编号
     * @return 台风位置信息，如果不存在返回null
     */
    public Map<String, Object> getTyphoonPosition(String tfid) {
        try {
            Map<String, Object> activeTyphoonData = typhoonService.getActiveTyphoonSummary();
            List<Map<String, Object>> typhoons = (List<Map<String, Object>>) activeTyphoonData.get("typhoons");
            
            if (typhoons != null) {
                for (Map<String, Object> typhoon : typhoons) {
                    if (tfid.equals(typhoon.get("tfid"))) {
                        return typhoon;
                    }
                }
            }
            
            return null;
        } catch (Exception e) {
            System.err.println("获取台风位置信息失败: " + e.getMessage());
            return null;
        }
    }
}
