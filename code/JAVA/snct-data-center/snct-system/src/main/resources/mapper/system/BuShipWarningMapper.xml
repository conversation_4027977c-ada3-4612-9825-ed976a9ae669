<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.BuShipWarningMapper">
    
    <resultMap type="BuShipWarning" id="BuShipWarningResult">
        <result property="id"    column="id"    />
        <result property="shipId"    column="ship_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="sn"    column="sn"    />
        <result property="deviceId"    column="device_id"    />
        <result property="dataKey"    column="data_key"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="level"    column="level"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBuShipWarningVo">
        select id, ship_id, dept_id, sn, device_id, data_key, name, type, level, status, remark, create_by, create_time, update_by, update_time from bu_ship_warning
    </sql>

    <select id="selectBuShipWarningList" parameterType="BuShipWarning" resultMap="BuShipWarningResult">
        <include refid="selectBuShipWarningVo"/>
        <where>  
            <if test="shipId != null "> and ship_id = #{shipId}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="sn != null  and sn != ''"> and sn = #{sn}</if>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="dataKey != null  and dataKey != ''"> and data_key = #{dataKey}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="level != null "> and level = #{level}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectBuShipWarningById" parameterType="Long" resultMap="BuShipWarningResult">
        <include refid="selectBuShipWarningVo"/>
        where id = #{id}
    </select>

    <insert id="insertBuShipWarning" parameterType="BuShipWarning" useGeneratedKeys="true" keyProperty="id">
        insert into bu_ship_warning
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shipId != null">ship_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="sn != null and sn != ''">sn,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="dataKey != null">data_key,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="type != null">type,</if>
            <if test="level != null">level,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shipId != null">#{shipId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="sn != null and sn != ''">#{sn},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="dataKey != null">#{dataKey},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="level != null">#{level},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBuShipWarning" parameterType="BuShipWarning">
        update bu_ship_warning
        <trim prefix="SET" suffixOverrides=",">
            <if test="shipId != null">ship_id = #{shipId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="sn != null and sn != ''">sn = #{sn},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="dataKey != null">data_key = #{dataKey},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="level != null">level = #{level},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBuShipWarningById" parameterType="Long">
        delete from bu_ship_warning where id = #{id}
    </delete>

    <delete id="deleteBuShipWarningByIds" parameterType="String">
        delete from bu_ship_warning where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>