package com.snct.typhoon.service.impl;


import com.snct.dctcore.commoncore.constants.RedisParameter;
import com.snct.typhoon.domain.Typhoon;
import com.snct.typhoon.domain.vo.TyphoonInfoVo;
import com.snct.typhoon.service.SelectTyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: SelectTyServiceImpl
 * @Description: 台风数据查询服务实现类
 * @author: wzewei
 * @date: 2025-08-19 16:43:35
 */
@Service
public class SelectTyServiceImpl implements SelectTyService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public List<Typhoon> selectTyphoonList(Integer yearsTime) {
        ValueOperations<String, List<Typhoon>> opsForValue1 = redisTemplate.opsForValue();
            return opsForValue1.get(RedisParameter.TYPHOON_LIST + yearsTime);
        }


    @Override
    public List<TyphoonInfoVo> selectTyphoonPoint(Integer tfId) {
        ValueOperations<String, List<TyphoonInfoVo>> opsForValue1 = redisTemplate.opsForValue();
        return opsForValue1.get(RedisParameter.TYPHOON_INFO + tfId);
    }

    @Override
    public Map<String, Object> selectActiveTyphoonSummary() {
        Map<String, Object> data = (Map<String, Object>) redisTemplate.opsForValue().get(RedisParameter.TYPHOON_ACTIVE_SUMMARY);
        return data != null ? data : new HashMap<>();
    }
}
